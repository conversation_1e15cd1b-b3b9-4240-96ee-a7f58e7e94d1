<!DOCTYPE html>
<html lang="de" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Print Masters - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#3B82F6',secondary:'#10B981'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="/js/theme.js" defer></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        :where([class^="ri-"])::before { content: "\f3c2"; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="/" class="flex items-center">
                <span id="header-logo" class="text-3xl font-['Pacifico'] text-primary" style="visibility: hidden;"></span>
                <img id="header-logo-image" class="h-10 hidden" src="" alt="Logo">
            </a>

            <div class="flex items-center space-x-4">
                <span id="user-email" class="text-gray-700"></span>
                <button id="theme-toggle" class="text-gray-700 hover:text-primary theme-toggle" title="Zum dunklen Modus wechseln">
                    <i id="theme-icon" class="ri-moon-line text-xl"></i>
                </button>
                <button id="logout-button" class="text-gray-700 hover:text-primary">
                    <i class="ri-logout-box-line text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Navigation Tabs -->
        <div class="mb-8 border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
                <li class="mr-2">
                    <button id="tab-products" class="inline-block p-4 border-b-2 border-primary text-primary rounded-t-lg active">
                        <i class="ri-shopping-bag-line mr-1"></i> Produkte
                    </button>
                </li>
                <li class="mr-2">
                    <button id="tab-categories" class="inline-block p-4 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 rounded-t-lg">
                        <i class="ri-price-tag-3-line mr-1"></i> Kategorien
                    </button>
                </li>
                <li class="mr-2">
                    <button id="tab-website-content" class="inline-block p-4 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 rounded-t-lg">
                        <i class="ri-layout-line mr-1"></i> Website-Inhalte
                    </button>
                </li>
            </ul>
        </div>

        <!-- Products Section -->
        <div id="products-section" class="block">
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-2xl font-bold">Produkte verwalten</h1>
                <button id="add-product-button" class="bg-primary text-white px-4 py-2 !rounded-button font-medium hover:bg-blue-600 transition">
                    <i class="ri-add-line mr-1"></i> Neues Produkt
                </button>
            </div>

        <!-- Product List -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="font-medium">Alle Produkte</h2>
                <div class="relative">
                    <input type="text" id="product-search" placeholder="Produkte suchen..." class="bg-gray-50 border border-gray-300 rounded-button px-4 py-2 pl-10 text-sm">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400">
                        <i class="ri-search-line"></i>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bild</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategorie</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preis</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody id="product-list" class="bg-white divide-y divide-gray-200">
                        <!-- Products will be loaded here -->
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">Produkte werden geladen...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        </div>

        <!-- Categories Section -->
        <div id="categories-section" class="hidden">
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-2xl font-bold">Kategorien verwalten</h1>
                <button id="add-category-button" class="bg-primary text-white px-4 py-2 !rounded-button font-medium hover:bg-blue-600 transition">
                    <i class="ri-add-line mr-1"></i> Neue Kategorie
                </button>
            </div>

            <!-- Category List -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="font-medium">Alle Kategorien</h2>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl Produkte</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                            </tr>
                        </thead>
                        <tbody id="category-list" class="bg-white divide-y divide-gray-200">
                            <!-- Categories will be loaded here -->
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-gray-500">Kategorien werden geladen...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Website Content Section -->
        <div id="website-content-section" class="hidden">
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-2xl font-bold">Website-Inhalte verwalten</h1>
                <div class="flex space-x-3">
                    <button id="save-content-button" class="bg-primary text-white px-4 py-2 !rounded-button font-medium hover:bg-blue-600 transition">
                        <i class="ri-save-line mr-1"></i> Änderungen speichern
                    </button>
                    <button id="view-website-button" class="bg-green-500 text-white px-4 py-2 !rounded-button font-medium hover:bg-green-600 transition">
                        <i class="ri-eye-line mr-1"></i> Website anzeigen
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <form id="website-content-form">
                        <!-- Logo Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">Logo</h2>

                            <!-- Logo Type Selection -->
                            <div class="mb-4">
                                <label class="block text-gray-700 mb-2">Logo-Typ</label>
                                <div class="flex space-x-4">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="logo-type" id="logo-type-text" value="text" class="mr-2" checked>
                                        <span>Text-Logo</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="logo-type" id="logo-type-image" value="image" class="mr-2">
                                        <span>Bild-Logo</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Text Logo Section -->
                            <div id="text-logo-section" class="mb-4">
                                <label for="logo-text" class="block text-gray-700 mb-2">Logo Text</label>
                                <input type="text" id="logo-text" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary">
                                <p class="text-gray-500 text-sm mt-1">Der Text, der als Logo angezeigt wird.</p>
                            </div>

                            <!-- Image Logo Section -->
                            <div id="image-logo-section" class="mb-4 hidden">
                                <label for="logo-image-url" class="block text-gray-700 mb-2">Logo Bild URL</label>
                                <input type="text" id="logo-image-url" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://beispiel.de/logo.png">
                                <p class="text-gray-500 text-sm mt-1">Gib die URL eines Bildes ein oder lade ein Bild hoch.</p>

                                <div class="mt-4">
                                    <label class="block text-gray-700 mb-2">Bild hochladen</label>
                                    <div class="flex items-center">
                                        <input type="file" id="logo-image-upload" accept="image/*" class="hidden">
                                        <label for="logo-image-upload" class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none">
                                            Bild auswählen
                                        </label>
                                        <span id="logo-selected-file-name" class="ml-2 text-sm text-gray-500">Keine Datei ausgewählt</span>
                                    </div>
                                    <div id="logo-upload-progress" class="hidden mt-2">
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div id="logo-upload-progress-bar" class="bg-primary h-2.5 rounded-full" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div id="logo-image-preview-container" class="hidden mt-2">
                                        <img id="logo-image-preview" class="max-w-full h-auto max-h-40 rounded-md" src="" alt="Vorschau">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hero Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">Hero-Bereich</h2>
                            <div class="mb-4">
                                <label for="hero-title" class="block text-gray-700 mb-2">Titel</label>
                                <input type="text" id="hero-title" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                            </div>
                            <div class="mb-4">
                                <label for="hero-text" class="block text-gray-700 mb-2">Text</label>
                                <textarea id="hero-text" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required></textarea>
                            </div>

                            <div class="mb-4">
                                <label for="hero-background-image" class="block text-gray-700 mb-2">Hintergrundbild URL</label>
                                <input type="text" id="hero-background-image" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://beispiel.de/bild.jpg">
                                <p class="text-gray-500 text-sm mt-1">Geben Sie die URL eines Bildes ein oder laden Sie ein Bild hoch.</p>
                            </div>

                            <div class="mb-4">
                                <label class="block text-gray-700 mb-2">Bild hochladen</label>
                                <div class="flex items-center">
                                    <input type="file" id="hero-image-upload" accept="image/*" class="hidden">
                                    <label for="hero-image-upload" class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none">
                                        Bild auswählen
                                    </label>
                                    <span id="hero-selected-file-name" class="ml-2 text-sm text-gray-500">Keine Datei ausgewählt</span>
                                </div>
                                <div id="hero-upload-progress" class="hidden mt-2">
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div id="hero-upload-progress-bar" class="bg-primary h-2.5 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div id="hero-image-preview-container" class="hidden mt-2">
                                    <img id="hero-image-preview" class="max-w-full h-auto max-h-40 rounded-md" src="" alt="Vorschau">
                                </div>
                            </div>
                        </div>

                        <!-- New Products Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">Neuheiten-Bereich</h2>
                            <div class="mb-4">
                                <label for="new-products-title" class="block text-gray-700 mb-2">Titel</label>
                                <input type="text" id="new-products-title" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                            </div>
                            <div class="mb-4">
                                <label for="new-products-text" class="block text-gray-700 mb-2">Text</label>
                                <textarea id="new-products-text" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required></textarea>
                            </div>
                        </div>

                        <!-- Products Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">Produkte-Bereich</h2>
                            <div class="mb-4">
                                <label for="products-title" class="block text-gray-700 mb-2">Titel</label>
                                <input type="text" id="products-title" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                            </div>
                            <div class="mb-4">
                                <label for="products-text" class="block text-gray-700 mb-2">Text</label>
                                <textarea id="products-text" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required></textarea>
                            </div>
                        </div>

                        <!-- Social Media Section -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4 pb-2 border-b border-gray-200">Social Media</h2>
                            <div class="mb-4">
                                <label for="whatsapp-url" class="block text-gray-700 mb-2">
                                    <i class="ri-whatsapp-line mr-1"></i> WhatsApp URL
                                </label>
                                <input type="text" id="whatsapp-url" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://wa.me/1234567890">
                                <p class="text-gray-500 text-sm mt-1">Gib die WhatsApp URL ein (z.B. https://wa.me/1234567890).</p>
                            </div>
                            <div class="mb-4">
                                <label for="instagram-url" class="block text-gray-700 mb-2">
                                    <i class="ri-instagram-line mr-1"></i> Instagram URL
                                </label>
                                <input type="text" id="instagram-url" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://instagram.com/username">
                                <p class="text-gray-500 text-sm mt-1">Gib die Instagram URL ein (z.B. https://instagram.com/username).</p>
                            </div>
                            <div class="mb-4">
                                <label for="tiktok-url" class="block text-gray-700 mb-2">
                                    <i class="ri-tiktok-line mr-1"></i> TikTok URL
                                </label>
                                <input type="text" id="tiktok-url" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://tiktok.com/@username">
                                <p class="text-gray-500 text-sm mt-1">Gib die TikTok URL ein (z.B. https://tiktok.com/@username).</p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Product Modal -->
    <div id="product-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 id="modal-title" class="text-xl font-bold">Neues Produkt hinzufügen</h2>
                    <button id="close-modal-button" class="text-gray-500 hover:text-gray-700">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <form id="product-form">
                    <input type="hidden" id="product-id">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="product-name" class="block text-gray-700 mb-2">Produktname</label>
                            <input type="text" id="product-name" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                        </div>

                        <div>
                            <label for="product-category" class="block text-gray-700 mb-2">Kategorie</label>
                            <select id="product-category" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                                <option value="">Kategorie auswählen</option>
                                <!-- Categories will be loaded here -->
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="product-description" class="block text-gray-700 mb-2">Beschreibung</label>
                        <textarea id="product-description" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="product-price" class="block text-gray-700 mb-2">Preis (€)</label>
                            <input type="number" id="product-price" step="0.01" min="0" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="product-is-new" class="mr-2">
                            <label for="product-is-new" class="text-gray-700">Als "Neu" markieren</label>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2">Produktbild</label>

                        <!-- Image Source Tabs -->
                        <div class="mb-4 border-b border-gray-200">
                            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
                                <li class="mr-2">
                                    <button type="button" id="tab-upload" class="inline-block p-4 border-b-2 border-primary text-primary rounded-t-lg active">
                                        Bild hochladen
                                    </button>
                                </li>
                                <li class="mr-2">
                                    <button type="button" id="tab-url" class="inline-block p-4 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 rounded-t-lg">
                                        Bild-URL
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <!-- Upload Tab Content -->
                        <div id="upload-tab-content">
                            <div class="flex items-center space-x-4">
                                <div class="flex-1">
                                    <div class="relative border border-gray-300 rounded-button px-4 py-2 bg-white">
                                        <input type="file" id="product-image-upload" accept="image/*" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
                                        <div class="flex items-center justify-between">
                                            <span id="file-name" class="text-gray-500 truncate">Keine Datei ausgewählt</span>
                                            <button type="button" class="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200">
                                                Durchsuchen
                                            </button>
                                        </div>
                                    </div>
                                    <div id="upload-progress" class="mt-2 hidden">
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div id="upload-progress-bar" class="bg-primary h-2.5 rounded-full" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div id="image-preview-container" class="hidden">
                                    <img id="image-preview" src="" alt="Vorschau" class="h-16 w-16 object-cover rounded border border-gray-300">
                                </div>
                            </div>
                        </div>

                        <!-- URL Tab Content -->
                        <div id="url-tab-content" class="hidden">
                            <input type="url" id="product-image-url" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" placeholder="https://beispiel.de/bild.jpg">
                            <p class="text-gray-500 text-sm mt-1">Gib die URL eines Bildes ein</p>
                        </div>

                        <input type="hidden" id="product-image" required>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-button" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Abbrechen</button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 !rounded-button font-medium hover:bg-blue-600 transition">Speichern</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <div id="category-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 id="category-modal-title" class="text-xl font-bold">Neue Kategorie hinzufügen</h2>
                    <button id="close-category-modal-button" class="text-gray-500 hover:text-gray-700">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <form id="category-form">
                    <input type="hidden" id="category-id">

                    <div class="mb-4">
                        <label for="category-name" class="block text-gray-700 mb-2">Kategoriename</label>
                        <input type="text" id="category-name" class="w-full px-4 py-2 border border-gray-300 rounded-button focus:border-primary focus:ring-1 focus:ring-primary" required>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-category-button" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Abbrechen</button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 !rounded-button font-medium hover:bg-blue-600 transition">Speichern</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Product Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
            <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Produkt löschen</h2>
                <p class="mb-6">Bist du sicher, dass du dieses Produkt löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.</p>

                <div class="flex justify-end space-x-3">
                    <button id="cancel-delete-button" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Abbrechen</button>
                    <button id="confirm-delete-button" class="bg-red-500 text-white px-4 py-2 !rounded-button font-medium hover:bg-red-600 transition">Löschen</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Category Confirmation Modal -->
    <div id="delete-category-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
            <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Kategorie löschen</h2>
                <p class="mb-6">Bist du sicher, dass du diese Kategorie löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.</p>
                <p class="mb-6 text-amber-600"><strong>Achtung:</strong> Wenn Produkte mit dieser Kategorie verknüpft sind, werden diese Produkte keiner Kategorie zugeordnet.</p>

                <div class="flex justify-end space-x-3">
                    <button id="cancel-delete-category-button" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Abbrechen</button>
                    <button id="confirm-delete-category-button" class="bg-red-500 text-white px-4 py-2 !rounded-button font-medium hover:bg-red-600 transition">Löschen</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://gprnzxmgvotjmwhajcig.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdwcm56eG1ndm90am13aGFqY2lnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDI0NTUsImV4cCI6MjA2MDk3ODQ1NX0.GRwD7bTR998FtNy4t7NUH1LNeIsGhfq6HTtFR-0U8QQ';

        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // DOM elements - Products
        const productList = document.getElementById('product-list');
        const productSearch = document.getElementById('product-search');
        const addProductButton = document.getElementById('add-product-button');
        const productModal = document.getElementById('product-modal');
        const modalTitle = document.getElementById('modal-title');
        const productForm = document.getElementById('product-form');
        const closeModalButton = document.getElementById('close-modal-button');
        const cancelButton = document.getElementById('cancel-button');
        const deleteModal = document.getElementById('delete-modal');
        const cancelDeleteButton = document.getElementById('cancel-delete-button');
        const confirmDeleteButton = document.getElementById('confirm-delete-button');
        const logoutButton = document.getElementById('logout-button');
        const userEmail = document.getElementById('user-email');

        // DOM elements - Navigation
        const tabProducts = document.getElementById('tab-products');
        const tabCategories = document.getElementById('tab-categories');
        const tabWebsiteContent = document.getElementById('tab-website-content');
        const productsSection = document.getElementById('products-section');
        const categoriesSection = document.getElementById('categories-section');
        const websiteContentSection = document.getElementById('website-content-section');

        // DOM elements - Website Content
        const websiteContentForm = document.getElementById('website-content-form');
        const saveContentButton = document.getElementById('save-content-button');

        // Header logo elements
        const headerLogo = document.getElementById('header-logo');
        const headerLogoImage = document.getElementById('header-logo-image');

        // Logo elements
        const logoTypeText = document.getElementById('logo-type-text');
        const logoTypeImage = document.getElementById('logo-type-image');
        const textLogoSection = document.getElementById('text-logo-section');
        const imageLogoSection = document.getElementById('image-logo-section');
        const logoText = document.getElementById('logo-text');
        const logoImageUrl = document.getElementById('logo-image-url');
        const logoImageUpload = document.getElementById('logo-image-upload');
        const logoSelectedFileName = document.getElementById('logo-selected-file-name');
        const logoUploadProgress = document.getElementById('logo-upload-progress');
        const logoUploadProgressBar = document.getElementById('logo-upload-progress-bar');
        const logoImagePreview = document.getElementById('logo-image-preview');
        const logoImagePreviewContainer = document.getElementById('logo-image-preview-container');

        // Hero section elements
        const heroTitle = document.getElementById('hero-title');
        const heroText = document.getElementById('hero-text');
        const heroBackgroundImage = document.getElementById('hero-background-image');
        const heroImageUpload = document.getElementById('hero-image-upload');
        const heroSelectedFileName = document.getElementById('hero-selected-file-name');
        const heroUploadProgress = document.getElementById('hero-upload-progress');
        const heroUploadProgressBar = document.getElementById('hero-upload-progress-bar');
        const heroImagePreview = document.getElementById('hero-image-preview');
        const heroImagePreviewContainer = document.getElementById('hero-image-preview-container');

        // Products section elements
        const newProductsTitle = document.getElementById('new-products-title');
        const newProductsText = document.getElementById('new-products-text');
        const productsTitle = document.getElementById('products-title');
        const productsText = document.getElementById('products-text');

        // Social media elements
        const whatsappUrl = document.getElementById('whatsapp-url');
        const instagramUrl = document.getElementById('instagram-url');
        const tiktokUrl = document.getElementById('tiktok-url');

        // Product form fields
        const productId = document.getElementById('product-id');
        const productName = document.getElementById('product-name');
        const productCategory = document.getElementById('product-category');
        const productDescription = document.getElementById('product-description');
        const productPrice = document.getElementById('product-price');
        const productIsNew = document.getElementById('product-is-new');
        const productImage = document.getElementById('product-image');
        const productImageUpload = document.getElementById('product-image-upload');
        const productImageUrl = document.getElementById('product-image-url');
        const fileName = document.getElementById('file-name');
        const imagePreview = document.getElementById('image-preview');
        const imagePreviewContainer = document.getElementById('image-preview-container');
        const uploadProgress = document.getElementById('upload-progress');
        const uploadProgressBar = document.getElementById('upload-progress-bar');
        const tabUpload = document.getElementById('tab-upload');
        const tabUrl = document.getElementById('tab-url');
        const uploadTabContent = document.getElementById('upload-tab-content');
        const urlTabContent = document.getElementById('url-tab-content');

        // Category elements
        const categoryList = document.getElementById('category-list');
        const addCategoryButton = document.getElementById('add-category-button');
        const categoryModal = document.getElementById('category-modal');
        const categoryModalTitle = document.getElementById('category-modal-title');
        const categoryForm = document.getElementById('category-form');
        const categoryId = document.getElementById('category-id');
        const categoryName = document.getElementById('category-name');
        const closeCategoryModalButton = document.getElementById('close-category-modal-button');
        const cancelCategoryButton = document.getElementById('cancel-category-button');
        const deleteCategoryModal = document.getElementById('delete-category-modal');
        const cancelDeleteCategoryButton = document.getElementById('cancel-delete-category-button');
        const confirmDeleteCategoryButton = document.getElementById('confirm-delete-category-button');

        // Track which tab is active
        let activeTab = 'upload';

        let currentProductId = null;
        let currentCategoryId = null;
        let categories = [];

        // Check if the website_content table exists and create it if it doesn't
        async function ensureWebsiteContentTable() {
            try {
                console.log('Checking if website_content table exists...');

                // Try to query the table
                const { error } = await supabaseClient
                    .from('website_content')
                    .select('id')
                    .limit(1);

                // If there's an error with code 42P01, the table doesn't exist
                if (error && error.code === '42P01') {
                    console.log('website_content table does not exist. Creating it...');

                    // Unfortunately, we can't create tables through the JavaScript client
                    // We'll need to alert the user to create the table manually
                    alert('Die Tabelle "website_content" existiert nicht in deiner Datenbank. Bitte erstelle diese Tabelle in deiner Supabase-Datenbank mit den folgenden Spalten: id (primary key), logo_type, logo_text, logo_image_url, hero_title, hero_text, hero_background_image, new_products_title, new_products_text, products_title, products_text, whatsapp_url, instagram_url, tiktok_url, updated_at.');

                    return false;
                } else if (error) {
                    console.error('Error checking website_content table:', error);
                    return false;
                }

                console.log('website_content table exists');

                // Now check if the social media columns exist
                await ensureSocialMediaColumns();

                return true;
            } catch (error) {
                console.error('Error ensuring website_content table:', error);
                return false;
            }
        }

        // Check if social media columns exist and add them if they don't
        async function ensureSocialMediaColumns() {
            try {
                console.log('Checking if social media columns exist...');

                // Try to execute a SQL query to check if the columns exist
                // We'll use RPC (Remote Procedure Call) to execute a SQL query
                const { data, error } = await supabaseClient.rpc('check_social_media_columns');

                if (error) {
                    console.error('Error checking social media columns:', error);

                    // If the RPC function doesn't exist, we need to create it
                    if (error.code === 'PGRST116') {
                        console.log('Creating check_social_media_columns function...');

                        // We can't create functions through the JavaScript client
                        // Let's try a different approach - try to select the columns directly
                        await checkAndAddSocialMediaColumns();
                    }
                    return;
                }

                console.log('Social media columns check result:', data);

                // If any column is missing, add it
                if (data && !data.all_columns_exist) {
                    await addMissingSocialMediaColumns(data);
                }
            } catch (error) {
                console.error('Error ensuring social media columns:', error);
            }
        }

        // Check if required columns exist by trying to select them
        async function checkAndAddSocialMediaColumns() {
            try {
                console.log('Checking required columns by direct selection...');

                // Check for logo_image_url column
                const { data: logoImageData, error: logoImageError } = await supabaseClient
                    .from('website_content')
                    .select('logo_image_url')
                    .limit(1);

                // If there's an error, the column might not exist
                if (logoImageError) {
                    console.log('logo_image_url column might not exist:', logoImageError);
                    await addRequiredColumn('logo_image_url');
                }

                // Try to select the whatsapp_url column
                const { data: whatsappData, error: whatsappError } = await supabaseClient
                    .from('website_content')
                    .select('whatsapp_url')
                    .limit(1);

                // If there's an error, the column might not exist
                if (whatsappError) {
                    console.log('whatsapp_url column might not exist:', whatsappError);
                    await addRequiredColumn('whatsapp_url');
                }

                // Try to select the instagram_url column
                const { data: instagramData, error: instagramError } = await supabaseClient
                    .from('website_content')
                    .select('instagram_url')
                    .limit(1);

                // If there's an error, the column might not exist
                if (instagramError) {
                    console.log('instagram_url column might not exist:', instagramError);
                    await addRequiredColumn('instagram_url');
                }

                // Try to select the tiktok_url column
                const { data: tiktokData, error: tiktokError } = await supabaseClient
                    .from('website_content')
                    .select('tiktok_url')
                    .limit(1);

                // If there's an error, the column might not exist
                if (tiktokError) {
                    console.log('tiktok_url column might not exist:', tiktokError);
                    await addRequiredColumn('tiktok_url');
                }
            } catch (error) {
                console.error('Error checking required columns by direct selection:', error);
            }
        }

        // Add a required column to the website_content table
        async function addRequiredColumn(columnName) {
            try {
                console.log(`Adding ${columnName} column to website_content table...`);

                // We can't execute ALTER TABLE through the JavaScript client
                // We need to alert the user to add the column manually
                alert(`Die Spalte "${columnName}" fehlt in der Tabelle "website_content". Bitte füge diese Spalte manuell in deiner Supabase-Datenbank hinzu. Du kannst dies in der Supabase-Konsole unter "Table Editor" > "website_content" > "Edit table" tun.`);

                // Return a promise that resolves after the user acknowledges the alert
                return new Promise(resolve => {
                    resolve();
                });
            } catch (error) {
                console.error(`Error adding ${columnName} column:`, error);
            }
        }

        // Add missing columns based on the check result
        async function addMissingSocialMediaColumns(checkResult) {
            if (!checkResult.has_logo_image_url) {
                await addRequiredColumn('logo_image_url');
            }

            if (!checkResult.has_whatsapp_url) {
                await addRequiredColumn('whatsapp_url');
            }

            if (!checkResult.has_instagram_url) {
                await addRequiredColumn('instagram_url');
            }

            if (!checkResult.has_tiktok_url) {
                await addRequiredColumn('tiktok_url');
            }
        }

        // Check if user is logged in
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('DOM content loaded, initializing admin panel');

                const { data: { user } } = await supabaseClient.auth.getUser();

                if (!user) {
                    console.log('No user found, redirecting to login page');
                    window.location.href = '/admin/index.html';
                    return;
                }

                console.log('User authenticated:', user.email);
                userEmail.textContent = user.email;

                // Ensure the website_content table exists
                await ensureWebsiteContentTable();

                // Set up event listeners first
                console.log('Setting up event listeners');
                setupEventListeners();

                // Load data
                console.log('Loading categories');
                await loadCategories();

                console.log('Loading products');
                await loadProducts();

                console.log('Loading website content form');
                await loadWebsiteContentForm();

                // Make sure products section is visible and active by default
                console.log('Setting initial active section to products');
                productsSection.classList.remove('hidden');
                tabProducts.classList.add('border-primary', 'text-primary');
                tabProducts.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                console.log('Admin panel initialization complete');
            } catch (error) {
                console.error('Error initializing admin panel:', error);
                alert('Fehler beim Initialisieren des Admin-Panels. Bitte die Konsole für Details prüfen.');
            }
        });

        // Load categories
        async function loadCategories() {
            try {
                const { data, error } = await supabaseClient
                    .from('categories')
                    .select('*')
                    .order('name', { ascending: true });

                if (error) {
                    console.error('Error loading categories:', error);
                    return;
                }

                categories = data;

                // Populate category dropdown
                productCategory.innerHTML = '<option value="">Kategorie auswählen</option>';

                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    productCategory.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Load products
        async function loadProducts(searchQuery = '') {
            try {
                let query = supabaseClient
                    .from('products')
                    .select(`
                        *,
                        categories(name)
                    `)
                    .order('created_at', { ascending: false });

                if (searchQuery) {
                    query = query.ilike('name', `%${searchQuery}%`);
                }

                const { data, error } = await query;

                if (error) {
                    console.error('Error loading products:', error);
                    productList.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">Fehler beim Laden der Produkte.</td></tr>';
                    return;
                }

                if (data.length === 0) {
                    productList.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">Keine Produkte gefunden.</td></tr>';
                    return;
                }

                productList.innerHTML = '';

                data.forEach(product => {
                    const row = document.createElement('tr');

                    const priceFormatted = new Intl.NumberFormat('de-DE', {
                        style: 'currency',
                        currency: 'EUR'
                    }).format(product.price);

                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <img src="${product.image_url}" alt="${product.name}" class="h-12 w-12 object-cover rounded">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${product.name}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">${product.categories ? product.categories.name : 'Keine Kategorie'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${priceFormatted}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center justify-between">
                                <div>
                                    ${product.is_new ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Neu</span>' : ''}
                                </div>
                                <div class="text-right text-sm font-medium">
                                    <button class="text-primary hover:text-blue-700 mr-3 edit-product" data-id="${product.id}">
                                        <i class="ri-edit-line"></i> Bearbeiten
                                    </button>
                                    <button class="text-red-500 hover:text-red-700 delete-product" data-id="${product.id}">
                                        <i class="ri-delete-bin-line"></i> Löschen
                                    </button>
                                </div>
                            </div>
                        </td>
                    `;

                    productList.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-product').forEach(button => {
                    button.addEventListener('click', () => editProduct(button.dataset.id));
                });

                document.querySelectorAll('.delete-product').forEach(button => {
                    button.addEventListener('click', () => showDeleteModal(button.dataset.id));
                });
            } catch (error) {
                console.error('Error loading products:', error);
                productList.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">Fehler beim Laden der Produkte.</td></tr>';
            }
        }

        // Set up event listeners
        function setupEventListeners() {
            // Add product button
            addProductButton.addEventListener('click', showAddProductModal);

            // Close modal buttons
            closeModalButton.addEventListener('click', hideProductModal);
            cancelButton.addEventListener('click', hideProductModal);

            // Delete modal buttons
            cancelDeleteButton.addEventListener('click', hideDeleteModal);
            confirmDeleteButton.addEventListener('click', deleteProduct);

            // Product form submission
            productForm.addEventListener('submit', saveProduct);

            // Product search
            productSearch.addEventListener('input', debounce(e => {
                loadProducts(e.target.value.trim());
            }, 300));

            // Logout button
            logoutButton.addEventListener('click', logout);

            // File upload
            productImageUpload.addEventListener('change', handleFileSelect);
            heroImageUpload.addEventListener('change', handleHeroImageSelect);
            logoImageUpload.addEventListener('change', handleLogoImageSelect);

            // Image URL input
            productImageUrl.addEventListener('input', handleUrlInput);
            productImageUrl.addEventListener('change', handleUrlInput);
            heroBackgroundImage.addEventListener('input', handleHeroUrlInput);
            heroBackgroundImage.addEventListener('change', handleHeroUrlInput);
            logoImageUrl.addEventListener('input', handleLogoUrlInput);
            logoImageUrl.addEventListener('change', handleLogoUrlInput);

            // Logo type selection
            logoTypeText.addEventListener('change', function() {
                if (this.checked) {
                    textLogoSection.classList.remove('hidden');
                    imageLogoSection.classList.add('hidden');

                    // Update header logo
                    headerLogo.textContent = logoText.value || 'logo';
                    headerLogo.classList.remove('hidden');
                    headerLogoImage.classList.add('hidden');
                }
            });

            logoTypeImage.addEventListener('change', function() {
                if (this.checked) {
                    imageLogoSection.classList.remove('hidden');
                    textLogoSection.classList.add('hidden');

                    // Update header logo if there's an image URL
                    if (logoImageUrl.value) {
                        headerLogo.classList.add('hidden');
                        headerLogoImage.src = logoImageUrl.value;
                        headerLogoImage.classList.remove('hidden');
                    }
                }
            });

            // Update header logo when text changes
            logoText.addEventListener('input', function() {
                if (logoTypeText.checked) {
                    headerLogo.textContent = this.value || 'logo';
                }
            });

            // Tab switching
            tabUpload.addEventListener('click', () => switchTab('upload'));
            tabUrl.addEventListener('click', () => switchTab('url'));

            // Navigation tabs
            tabProducts.addEventListener('click', () => switchSection('products'));
            tabCategories.addEventListener('click', () => switchSection('categories'));
            tabWebsiteContent.addEventListener('click', () => switchSection('website-content'));

            // Category management
            addCategoryButton.addEventListener('click', showAddCategoryModal);
            closeCategoryModalButton.addEventListener('click', hideCategoryModal);
            cancelCategoryButton.addEventListener('click', hideCategoryModal);
            categoryForm.addEventListener('submit', saveCategory);
            cancelDeleteCategoryButton.addEventListener('click', hideDeleteCategoryModal);
            confirmDeleteCategoryButton.addEventListener('click', deleteCategory);

            // Save website content
            console.log('Setting up save content button event listener:', !!saveContentButton);
            saveContentButton.addEventListener('click', function(e) {
                console.log('Save content button clicked');
                saveWebsiteContent(e);
            });

            // View website button
            const viewWebsiteButton = document.getElementById('view-website-button');
            console.log('Setting up view website button event listener:', !!viewWebsiteButton);
            viewWebsiteButton.addEventListener('click', async function() {
                console.log('View website button clicked');

                try {
                    // First, ensure the latest content is saved to the database
                    if (localStorage.getItem('contentUpdated') === 'true') {
                        console.log('Content was updated but not yet viewed, ensuring database is up to date');

                        // Get the latest content from the form
                        const content = {
                            logo_type: logoTypeText.checked ? 'text' : 'image',
                            logo_text: logoText.value.trim(),
                            logo_image_url: logoTypeImage.checked ? logoImageUrl.value.trim() : '',
                            hero_title: heroTitle.value.trim(),
                            hero_text: heroText.value.trim(),
                            hero_background_image: heroBackgroundImage.value.trim(),
                            new_products_title: newProductsTitle.value.trim(),
                            new_products_text: newProductsText.value.trim(),
                            products_title: productsTitle.value.trim(),
                            products_text: productsText.value.trim(),
                            whatsapp_url: whatsappUrl.value.trim(),
                            instagram_url: instagramUrl.value.trim(),
                            tiktok_url: tiktokUrl.value.trim(),
                            updated_at: new Date().toISOString()
                        };

                        // Force an update to the database
                        const { error } = await supabaseClient
                            .from('website_content')
                            .upsert({ id: 1, ...content });

                        if (error) {
                            console.error('Error ensuring content is up to date:', error);
                        } else {
                            console.log('Content successfully updated before viewing website');
                        }

                        // Clear the flag
                        localStorage.removeItem('contentUpdated');
                    }

                    // Set a flag in localStorage to indicate that the website should refresh content
                    // This will be picked up by the main website
                    localStorage.setItem('refreshWebsiteContent', 'true');
                    localStorage.setItem('refreshTimestamp', new Date().getTime().toString());
                    console.log('Set refresh flag in localStorage');

                    // Open the main website in a new tab with a cache-busting parameter
                    const timestamp = new Date().getTime();
                    window.open('/?t=' + timestamp + '&refresh=true', '_blank');
                } catch (error) {
                    console.error('Error in view website button handler:', error);

                    // Still open the website even if there was an error
                    window.open('/?t=' + new Date().getTime(), '_blank');
                }
            });
        }

        // Switch between main sections (products/categories/website content)
        function switchSection(section) {
            console.log('Switching to section:', section);

            // Reset all tabs
            tabProducts.classList.remove('border-primary', 'text-primary');
            tabProducts.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

            tabCategories.classList.remove('border-primary', 'text-primary');
            tabCategories.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

            tabWebsiteContent.classList.remove('border-primary', 'text-primary');
            tabWebsiteContent.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

            // Hide all sections
            productsSection.classList.add('hidden');
            categoriesSection.classList.add('hidden');
            websiteContentSection.classList.add('hidden');

            // Activate the selected section
            if (section === 'products') {
                console.log('Activating products section');
                tabProducts.classList.add('border-primary', 'text-primary');
                tabProducts.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                productsSection.classList.remove('hidden');
            } else if (section === 'categories') {
                console.log('Activating categories section');
                tabCategories.classList.add('border-primary', 'text-primary');
                tabCategories.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                categoriesSection.classList.remove('hidden');
                loadCategoriesWithCount(); // Load categories with product count
            } else if (section === 'website-content') {
                console.log('Activating website content section');
                tabWebsiteContent.classList.add('border-primary', 'text-primary');
                tabWebsiteContent.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                websiteContentSection.classList.remove('hidden');
            }
        }

        // Function to fetch website content
        async function fetchWebsiteContent() {
            try {
                console.log('Fetching website content');

                // Directly fetch the record with ID 1
                const { data, error } = await supabaseClient
                    .from('website_content')
                    .select('*')
                    .eq('id', 1)
                    .single();

                console.log('Fetch result:', { data, error });

                if (error) {
                    console.error('Error fetching website content:', error);

                    // Return default values if there's an error
                    return {
                        logo_type: 'text',
                        logo_text: 'logo',
                        logo_image_url: '',
                        hero_title: 'Individuelle 3D-Druck Lösungen',
                        hero_text: 'Verwandeln Sie Ihre Ideen in die Realität mit unseren Premium 3D-Druck Dienstleistungen. Von Prototypen bis zu fertigen Produkten bringen wir Ihre Vision mit Präzision und Qualität zum Leben.',
                        new_products_title: 'Neuheiten',
                        new_products_text: 'Entdecken Sie unsere neuesten 3D-Druck Kreationen, frisch vom Drucker und bereit für Sie zum Erkunden.',
                        products_title: 'Unsere Produkte',
                        products_text: 'Durchstöbern Sie unsere Auswahl an hochwertigen 3D-Druck Produkten und finden Sie das Perfekte für Ihre Bedürfnisse.',
                        whatsapp_url: 'https://wa.me/1234567890',
                        instagram_url: 'https://instagram.com',
                        tiktok_url: 'https://tiktok.com'
                    };
                }

                return data;
            } catch (error) {
                console.error('Error fetching website content:', error);
                console.error('Error details:', error.message, error.stack);

                // Return default values if there's an exception
                return {
                    logo_type: 'text',
                    logo_text: 'logo',
                    logo_image_url: '',
                    hero_title: 'Individuelle 3D-Druck Lösungen',
                    hero_text: 'Verwandeln Sie Ihre Ideen in die Realität mit unseren Premium 3D-Druck Dienstleistungen. Von Prototypen bis zu fertigen Produkten bringen wir Ihre Vision mit Präzision und Qualität zum Leben.',
                    new_products_title: 'Neuheiten',
                    new_products_text: 'Entdecken Sie unsere neuesten 3D-Druck Kreationen, frisch vom Drucker und bereit für Sie zum Erkunden.',
                    products_title: 'Unsere Produkte',
                    products_text: 'Durchstöbern Sie unsere Auswahl an hochwertigen 3D-Druck Produkten und finden Sie das Perfekte für Ihre Bedürfnisse.',
                    whatsapp_url: 'https://wa.me/1234567890',
                    instagram_url: 'https://instagram.com',
                    tiktok_url: 'https://tiktok.com'
                };
            }
        }

        // Function to update website content
        async function updateWebsiteContent(content) {
            try {
                console.log('Updating website content with:', content);

                // We know there's already a record with ID 1 from our direct test
                // Let's try to update it directly
                console.log('Updating content with ID 1');
                const result = await supabaseClient
                    .from('website_content')
                    .update(content)
                    .eq('id', 1);

                console.log('Update result:', result);

                if (result.error) {
                    console.error('Error updating website content:', result.error);

                    // If update fails, try inserting
                    console.log('Update failed, trying insert instead');
                    const insertResult = await supabaseClient
                        .from('website_content')
                        .insert([content]);

                    console.log('Insert result:', insertResult);

                    if (insertResult.error) {
                        console.error('Error inserting website content:', insertResult.error);
                        return false;
                    }

                    return true;
                }

                console.log('Content updated successfully');
                return true;
            } catch (error) {
                console.error('Error updating website content:', error);
                console.error('Error details:', error.message, error.stack);
                return false;
            }
        }

        // Load website content form
        async function loadWebsiteContentForm() {
            try {
                const content = await fetchWebsiteContent();

                if (!content) return;

                // Fill logo fields
                logoText.value = content.logo_text || '';

                // Check if logo_image_url exists in the content
                const hasLogoImageUrl = 'logo_image_url' in content;

                // Set logo type and show/hide appropriate sections
                if (content.logo_type === 'image' && hasLogoImageUrl && content.logo_image_url) {
                    logoTypeImage.checked = true;
                    textLogoSection.classList.add('hidden');
                    imageLogoSection.classList.remove('hidden');

                    logoImageUrl.value = content.logo_image_url || '';

                    // Show logo image preview if available
                    if (content.logo_image_url) {
                        logoImagePreview.src = content.logo_image_url;
                        logoImagePreviewContainer.classList.remove('hidden');

                        // Create a new image to preload the logo
                        const img = new Image();

                        // Set up event handlers
                        img.onload = function() {
                            // Image has loaded successfully, now apply it
                            headerLogo.classList.add('hidden');
                            headerLogoImage.src = content.logo_image_url;
                            headerLogoImage.classList.remove('hidden');
                        };

                        img.onerror = function() {
                            // Image failed to load, use text logo as fallback
                            console.error('Failed to load logo image, using text logo as fallback');
                            headerLogo.textContent = content.logo_text || '';
                            headerLogo.style.visibility = 'visible';
                            headerLogo.classList.remove('hidden');
                            headerLogoImage.classList.add('hidden');
                        };

                        // Start loading the image
                        img.src = content.logo_image_url;
                    } else {
                        logoImagePreviewContainer.classList.add('hidden');
                    }
                } else {
                    // Default to text logo if image logo is not available or logo_image_url is missing
                    logoTypeText.checked = true;
                    textLogoSection.classList.remove('hidden');
                    imageLogoSection.classList.add('hidden');

                    // Update header logo
                    headerLogo.textContent = content.logo_text || '';
                    headerLogo.style.visibility = 'visible';
                    headerLogo.classList.remove('hidden');
                    headerLogoImage.classList.add('hidden');

                    // If logo_image_url is missing, check the column
                    if (!hasLogoImageUrl) {
                        console.log('logo_image_url is missing in content, checking if column exists');
                        const { error: logoImageError } = await supabaseClient
                            .from('website_content')
                            .select('logo_image_url')
                            .limit(1);

                        if (logoImageError) {
                            console.log('logo_image_url column does not exist, alerting user');
                            await addRequiredColumn('logo_image_url');
                        }
                    }
                }

                // Fill hero section fields
                heroTitle.value = content.hero_title || '';
                heroText.value = content.hero_text || '';
                heroBackgroundImage.value = content.hero_background_image || '';

                // Fill products section fields
                newProductsTitle.value = content.new_products_title || '';
                newProductsText.value = content.new_products_text || '';
                productsTitle.value = content.products_title || '';
                productsText.value = content.products_text || '';

                // Fill social media fields
                whatsappUrl.value = content.whatsapp_url || '';
                instagramUrl.value = content.instagram_url || '';
                tiktokUrl.value = content.tiktok_url || '';

                // Show hero background image preview if available
                if (content.hero_background_image) {
                    heroImagePreview.src = content.hero_background_image;
                    heroImagePreviewContainer.classList.remove('hidden');
                } else {
                    heroImagePreviewContainer.classList.add('hidden');
                }

                // Reset file upload fields
                heroImageUpload.value = '';
                heroSelectedFileName.textContent = 'Keine Datei ausgewählt';
                heroUploadProgress.classList.add('hidden');

                logoImageUpload.value = '';
                logoSelectedFileName.textContent = 'Keine Datei ausgewählt';
                logoUploadProgress.classList.add('hidden');
            } catch (error) {
                console.error('Error loading website content form:', error);
            }
        }

        // Save website content
        async function saveWebsiteContent(e) {
            if (e) e.preventDefault();

            try {
                console.log('Save website content triggered');

                // Check if the form elements exist
                console.log('Form elements check:', {
                    logoText: !!logoText,
                    heroTitle: !!heroTitle,
                    heroText: !!heroText,
                    newProductsTitle: !!newProductsTitle,
                    newProductsText: !!newProductsText,
                    productsTitle: !!productsTitle,
                    productsText: !!productsText
                });

                // Validate that all required fields have values
                if (logoTypeText.checked && !logoText.value) {
                    alert('Bitte gib einen Logo-Text ein, wenn du Text-Logo ausgewählt hast.');
                    return;
                }

                if (logoTypeImage.checked && !logoImageUrl.value) {
                    alert('Bitte gib eine Logo-Bild-URL ein oder lade ein Bild hoch, wenn du Bild-Logo ausgewählt hast.');
                    return;
                }

                if (!heroTitle.value || !heroText.value ||
                    !newProductsTitle.value || !newProductsText.value ||
                    !productsTitle.value || !productsText.value) {
                    alert('Bitte fülle alle Pflichtfelder aus.');
                    return;
                }

                // Check if images are too large (if they're base64 images)
                const checkImageSize = (value, name) => {
                    if (value && value.startsWith('data:image')) {
                        // Rough estimate: 1 MB = ~1.37 million characters in base64
                        if (value.length > 2000000) { // ~1.5 MB
                            return !confirm(`Das ${name} ist sehr groß, was die Ladezeit der Website beeinträchtigen könnte. Möchtest du fortfahren?`);
                        }
                    }
                    return false;
                };

                if (checkImageSize(heroBackgroundImage.value, 'Hintergrundbild')) {
                    return;
                }

                if (logoTypeImage.checked && checkImageSize(logoImageUrl.value, 'Logo-Bild')) {
                    return;
                }

                // Create the base content object with required fields
                const baseContent = {
                    logo_type: logoTypeText.checked ? 'text' : 'image',
                    logo_text: logoText.value.trim(),
                    logo_image_url: logoTypeImage.checked ? logoImageUrl.value.trim() : '',
                    hero_title: heroTitle.value.trim(),
                    hero_text: heroText.value.trim(),
                    hero_background_image: heroBackgroundImage.value.trim(),
                    new_products_title: newProductsTitle.value.trim(),
                    new_products_text: newProductsText.value.trim(),
                    products_title: productsTitle.value.trim(),
                    products_text: productsText.value.trim(),
                    updated_at: new Date().toISOString()
                };

                // Try to add required fields if their columns exist
                try {
                    // Check if required columns exist by trying to select them
                    const { data: columnsCheck, error: columnsError } = await supabaseClient
                        .from('website_content')
                        .select('logo_image_url, whatsapp_url, instagram_url, tiktok_url')
                        .limit(1);

                    console.log('Required columns check:', { data: columnsCheck, error: columnsError });

                    // If there's no error, all columns exist
                    if (!columnsError) {
                        console.log('All required columns exist, adding values to content object');
                        // Logo image URL is already in baseContent, but we'll check if the column exists
                        // Social media URLs
                        baseContent.whatsapp_url = whatsappUrl.value.trim();
                        baseContent.instagram_url = instagramUrl.value.trim();
                        baseContent.tiktok_url = tiktokUrl.value.trim();
                    } else {
                        console.log('Some columns might not exist, checking individually');

                        // Check logo_image_url column
                        const { error: logoImageError } = await supabaseClient
                            .from('website_content')
                            .select('logo_image_url')
                            .limit(1);

                        if (logoImageError) {
                            console.log('logo_image_url column does not exist, removing from content');
                            // Remove logo_image_url from baseContent if the column doesn't exist
                            delete baseContent.logo_image_url;
                        }

                        // Check each social media column individually
                        const { error: whatsappError } = await supabaseClient
                            .from('website_content')
                            .select('whatsapp_url')
                            .limit(1);

                        if (!whatsappError) {
                            baseContent.whatsapp_url = whatsappUrl.value.trim();
                        }

                        const { error: instagramError } = await supabaseClient
                            .from('website_content')
                            .select('instagram_url')
                            .limit(1);

                        if (!instagramError) {
                            baseContent.instagram_url = instagramUrl.value.trim();
                        }

                        const { error: tiktokError } = await supabaseClient
                            .from('website_content')
                            .select('tiktok_url')
                            .limit(1);

                        if (!tiktokError) {
                            baseContent.tiktok_url = tiktokUrl.value.trim();
                        }
                    }
                } catch (columnsError) {
                    console.error('Error checking required columns:', columnsError);
                    // Continue without the problematic fields
                    // Remove logo_image_url to be safe
                    delete baseContent.logo_image_url;
                }

                const content = baseContent;
                console.log('Content to save:', content);

                // Check if the Supabase client is initialized
                console.log('Supabase client check:', !!supabaseClient);

                // First, try to update the existing record
                const { data: updateData, error: updateError } = await supabaseClient
                    .from('website_content')
                    .update(content)
                    .eq('id', 1);

                console.log('Direct update result:', { data: updateData, error: updateError });

                if (updateError) {
                    console.error('Error updating content, trying upsert:', updateError);

                    // If update fails, try an upsert operation
                    const { data: upsertData, error: upsertError } = await supabaseClient
                        .from('website_content')
                        .upsert({ id: 1, ...content });

                    console.log('Upsert result:', { data: upsertData, error: upsertError });

                    if (upsertError) {
                        console.error('Upsert also failed, trying insert:', upsertError);

                        // If upsert fails, try a direct insert
                        const { data: insertData, error: insertError } = await supabaseClient
                            .from('website_content')
                            .insert([{ id: 1, ...content }]);

                        console.log('Insert result:', { data: insertData, error: insertError });

                        if (insertError) {
                            console.error('All database operations failed:', insertError);

                            // Check if the error is related to missing columns
                            if (insertError.message && insertError.message.includes('column') && insertError.message.includes('does not exist')) {
                                // Extract the column name from the error message
                                const columnMatch = insertError.message.match(/column "([^"]+)" of relation/);
                                const columnName = columnMatch ? columnMatch[1] : 'unknown';

                                // Alert the user about the missing column
                                alert(`Die Spalte "${columnName}" fehlt in der Tabelle "website_content". Bitte füge diese Spalte manuell in deiner Supabase-Datenbank hinzu. Du kannst dies in der Supabase-Konsole unter "Table Editor" > "website_content" > "Edit table" tun.`);

                                // Try to save without the problematic column
                                delete content[columnName];

                                // Try again with the modified content
                                const { error: retryError } = await supabaseClient
                                    .from('website_content')
                                    .upsert({ id: 1, ...content });

                                if (retryError) {
                                    throw new Error('Alle Datenbankoperationen sind fehlgeschlagen: ' + retryError.message);
                                }
                            } else {
                                throw new Error('Alle Datenbankoperationen sind fehlgeschlagen: ' + insertError.message);
                            }
                        }
                    }
                }

                // Set a flag to indicate content has been updated
                localStorage.setItem('contentUpdated', 'true');
                localStorage.setItem('contentUpdateTimestamp', new Date().getTime().toString());

                alert('Website-Inhalte wurden erfolgreich gespeichert!');

                // Refresh the form to show the latest content
                await loadWebsiteContentForm();

            } catch (error) {
                console.error('Error saving website content:', error);
                console.error('Error details:', error.message, error.stack);
                alert('Fehler beim Speichern der Website-Inhalte: ' + error.message);
            }
        }

        // Switch between tabs
        function switchTab(tab) {
            activeTab = tab;

            // Update tab styling
            if (tab === 'upload') {
                tabUpload.classList.add('border-primary', 'text-primary');
                tabUpload.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                tabUrl.classList.remove('border-primary', 'text-primary');
                tabUrl.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                uploadTabContent.classList.remove('hidden');
                urlTabContent.classList.add('hidden');
            } else {
                tabUrl.classList.add('border-primary', 'text-primary');
                tabUrl.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                tabUpload.classList.remove('border-primary', 'text-primary');
                tabUpload.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                urlTabContent.classList.remove('hidden');
                uploadTabContent.classList.add('hidden');
            }
        }

        // Handle file selection
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Update file name display
            fileName.textContent = file.name;

            // Process the file (this will handle both preview and storage)
            uploadFile(file);
        }

        // Handle URL input
        function handleUrlInput(e) {
            const url = e.target.value.trim();

            if (url) {
                // Set the image URL in the hidden input
                productImage.value = url;

                // Show image preview
                imagePreview.src = url;
                imagePreviewContainer.classList.remove('hidden');
            } else {
                imagePreviewContainer.classList.add('hidden');
                productImage.value = '';
            }
        }

        // Handle hero background image URL input
        function handleHeroUrlInput(e) {
            const url = e.target.value.trim();

            if (url) {
                heroImagePreview.src = url;
                heroImagePreviewContainer.classList.remove('hidden');
            } else {
                heroImagePreviewContainer.classList.add('hidden');
            }
        }

        // Handle logo image URL input
        function handleLogoUrlInput(e) {
            const url = e.target.value.trim();

            if (url) {
                logoImagePreview.src = url;
                logoImagePreviewContainer.classList.remove('hidden');

                // Update header logo if image logo type is selected
                if (logoTypeImage.checked) {
                    headerLogo.classList.add('hidden');
                    headerLogoImage.src = url;
                    headerLogoImage.classList.remove('hidden');
                }
            } else {
                logoImagePreviewContainer.classList.add('hidden');

                // If URL is empty and image logo type is selected, show text logo in header
                if (logoTypeImage.checked) {
                    headerLogo.textContent = logoText.value || 'logo';
                    headerLogo.classList.remove('hidden');
                    headerLogoImage.classList.add('hidden');
                }
            }
        }

        // Handle hero image file selection
        async function handleHeroImageSelect(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Update file name display
            heroSelectedFileName.textContent = file.name;

            // Show upload progress
            heroUploadProgress.classList.remove('hidden');
            heroUploadProgressBar.style.width = '0%';

            try {
                // Read the file as base64
                const reader = new FileReader();

                reader.onprogress = (event) => {
                    if (event.lengthComputable) {
                        const percent = Math.round((event.loaded / event.total) * 100);
                        heroUploadProgressBar.style.width = `${percent}%`;
                    }
                };

                reader.onload = function(e) {
                    // Set the base64 data as the image URL
                    const base64Data = e.target.result;
                    heroBackgroundImage.value = base64Data;

                    // Update image preview
                    heroImagePreview.src = base64Data;
                    heroImagePreviewContainer.classList.remove('hidden');

                    console.log('Hero background image encoded as base64 successfully');
                };

                reader.onerror = function() {
                    console.error('Error reading hero background image file');
                    alert('Fehler beim Lesen der Datei');
                };

                // Start reading the file
                reader.readAsDataURL(file);

            } catch (error) {
                console.error('Error handling hero background image:', error);
                alert('Fehler beim Verarbeiten des Bildes: ' + error.message);
            } finally {
                // Hide upload progress after a short delay
                setTimeout(() => {
                    heroUploadProgress.classList.add('hidden');
                    heroUploadProgressBar.style.width = '0%';
                }, 1000);
            }
        }

        // Handle logo image file selection
        async function handleLogoImageSelect(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Update file name display
            logoSelectedFileName.textContent = file.name;

            // Show upload progress
            logoUploadProgress.classList.remove('hidden');

            try {
                // Read the file as a data URL
                const reader = new FileReader();

                reader.onprogress = function(event) {
                    if (event.lengthComputable) {
                        const percentComplete = Math.round((event.loaded / event.total) * 100);
                        logoUploadProgressBar.style.width = percentComplete + '%';
                    }
                };

                reader.onload = function(e) {
                    // Set the image URL to the data URL
                    logoImageUrl.value = e.target.result;

                    // Show image preview
                    logoImagePreview.src = e.target.result;
                    logoImagePreviewContainer.classList.remove('hidden');

                    // Update header logo if image logo type is selected
                    if (logoTypeImage.checked) {
                        headerLogo.classList.add('hidden');
                        headerLogoImage.src = e.target.result;
                        headerLogoImage.classList.remove('hidden');
                    }

                    // Hide progress bar
                    logoUploadProgress.classList.add('hidden');
                };

                reader.readAsDataURL(file);
            } catch (error) {
                console.error('Error handling logo file:', error);
                alert(`Fehler beim Verarbeiten der Datei: ${error.message}`);
                logoUploadProgress.classList.add('hidden');
            }
        }

        // Handle file upload using base64 encoding
        async function uploadFile(file) {
            try {
                // Show progress bar
                uploadProgress.classList.remove('hidden');
                uploadProgressBar.style.width = '0%';

                // Read the file as base64
                const reader = new FileReader();

                reader.onprogress = (event) => {
                    if (event.lengthComputable) {
                        const percent = Math.round((event.loaded / event.total) * 100);
                        uploadProgressBar.style.width = `${percent}%`;
                    }
                };

                reader.onload = function(e) {
                    // Set the base64 data as the image URL
                    const base64Data = e.target.result;
                    productImage.value = base64Data;

                    // Update image preview
                    imagePreview.src = base64Data;
                    imagePreviewContainer.classList.remove('hidden');

                    // Hide progress bar after a short delay
                    setTimeout(() => {
                        uploadProgress.classList.add('hidden');
                        uploadProgressBar.style.width = '0%';
                    }, 1000);
                };

                reader.onerror = function() {
                    console.error('Error reading file');
                    alert('Fehler beim Lesen der Datei');
                    uploadProgress.classList.add('hidden');
                    uploadProgressBar.style.width = '0%';
                };

                // Start reading the file
                reader.readAsDataURL(file);

            } catch (error) {
                console.error('Error handling file:', error);
                alert(`Fehler beim Verarbeiten der Datei: ${error.message}`);
                uploadProgress.classList.add('hidden');
                uploadProgressBar.style.width = '0%';
            }
        }

        // Show add product modal
        function showAddProductModal() {
            modalTitle.textContent = 'Neues Produkt hinzufügen';
            productForm.reset();
            productId.value = '';
            currentProductId = null;
            imagePreviewContainer.classList.add('hidden');
            uploadProgress.classList.add('hidden');
            uploadProgressBar.style.width = '0%';
            fileName.textContent = 'Keine Datei ausgewählt';
            productImageUrl.value = '';

            // Default to upload tab
            switchTab('upload');

            productModal.classList.remove('hidden');
        }

        // Show edit product modal
        async function editProduct(id) {
            try {
                modalTitle.textContent = 'Produkt bearbeiten';

                const { data, error } = await supabaseClient
                    .from('products')
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) {
                    console.error('Error fetching product:', error);
                    return;
                }

                productId.value = data.id;
                currentProductId = data.id;
                productName.value = data.name;
                productCategory.value = data.category_id || '';
                productDescription.value = data.description;
                productPrice.value = data.price;
                productIsNew.checked = data.is_new;
                productImage.value = data.image_url;

                // Reset file input
                productImageUpload.value = '';
                fileName.textContent = 'Keine Datei ausgewählt';

                // Set URL input
                productImageUrl.value = data.image_url || '';

                // Show image preview if there's an image URL
                if (data.image_url) {
                    imagePreview.src = data.image_url;
                    imagePreviewContainer.classList.remove('hidden');

                    // Determine which tab to show based on the URL
                    if (data.image_url.startsWith('data:image')) {
                        switchTab('upload');
                    } else {
                        switchTab('url');
                    }
                } else {
                    imagePreviewContainer.classList.add('hidden');
                    switchTab('upload');
                }

                productModal.classList.remove('hidden');
            } catch (error) {
                console.error('Error editing product:', error);
            }
        }

        // Hide product modal
        function hideProductModal() {
            productModal.classList.add('hidden');
            productForm.reset();
            imagePreviewContainer.classList.add('hidden');
            uploadProgress.classList.add('hidden');
            uploadProgressBar.style.width = '0%';
            fileName.textContent = 'Keine Datei ausgewählt';
            productImageUrl.value = '';
            productImage.value = '';
        }

        // Show delete confirmation modal
        function showDeleteModal(id) {
            currentProductId = id;
            deleteModal.classList.remove('hidden');
        }

        // Hide delete confirmation modal
        function hideDeleteModal() {
            deleteModal.classList.add('hidden');
            currentProductId = null;
        }

        // Save product (add or update)
        async function saveProduct(e) {
            e.preventDefault();

            try {
                const productData = {
                    name: productName.value,
                    category_id: productCategory.value,
                    description: productDescription.value,
                    price: parseFloat(productPrice.value),
                    is_new: productIsNew.checked,
                    image_url: productImage.value
                };

                let error;

                if (currentProductId) {
                    // Update existing product
                    const { error: updateError } = await supabaseClient
                        .from('products')
                        .update(productData)
                        .eq('id', currentProductId);

                    error = updateError;
                } else {
                    // Add new product
                    const { error: insertError } = await supabaseClient
                        .from('products')
                        .insert([productData]);

                    error = insertError;
                }

                if (error) {
                    console.error('Error saving product:', error);
                    return;
                }

                hideProductModal();
                loadProducts();
            } catch (error) {
                console.error('Error saving product:', error);
            }
        }

        // Delete product
        async function deleteProduct() {
            try {
                const { error } = await supabaseClient
                    .from('products')
                    .delete()
                    .eq('id', currentProductId);

                if (error) {
                    console.error('Error deleting product:', error);
                    return;
                }

                hideDeleteModal();
                loadProducts();
            } catch (error) {
                console.error('Error deleting product:', error);
            }
        }

        // Logout
        async function logout() {
            try {
                await supabaseClient.auth.signOut();
                window.location.href = '/admin/index.html';
            } catch (error) {
                console.error('Error logging out:', error);
            }
        }

        // Load categories with product count
        async function loadCategoriesWithCount() {
            try {
                // First get all categories
                const { data: categoriesData, error: categoriesError } = await supabaseClient
                    .from('categories')
                    .select('*')
                    .order('name', { ascending: true });

                if (categoriesError) {
                    console.error('Error loading categories:', categoriesError);
                    categoryList.innerHTML = '<tr><td colspan="3" class="px-6 py-4 text-center text-red-500">Fehler beim Laden der Kategorien.</td></tr>';
                    return;
                }

                if (categoriesData.length === 0) {
                    categoryList.innerHTML = '<tr><td colspan="3" class="px-6 py-4 text-center text-gray-500">Keine Kategorien gefunden.</td></tr>';
                    return;
                }

                // Get product counts for each category
                const { data: productsData, error: productsError } = await supabaseClient
                    .from('products')
                    .select('category_id');

                if (productsError) {
                    console.error('Error loading product counts:', productsError);
                }

                // Count products per category
                const categoryCounts = {};
                if (productsData) {
                    productsData.forEach(product => {
                        if (product.category_id) {
                            categoryCounts[product.category_id] = (categoryCounts[product.category_id] || 0) + 1;
                        }
                    });
                }

                categoryList.innerHTML = '';

                categoriesData.forEach(category => {
                    const row = document.createElement('tr');
                    const productCount = categoryCounts[category.id] || 0;

                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${category.name}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">${productCount} Produkt${productCount !== 1 ? 'e' : ''}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button class="text-primary hover:text-blue-700 mr-3 edit-category" data-id="${category.id}" data-name="${category.name}">
                                <i class="ri-edit-line"></i> Bearbeiten
                            </button>
                            <button class="text-red-500 hover:text-red-700 delete-category" data-id="${category.id}">
                                <i class="ri-delete-bin-line"></i> Löschen
                            </button>
                        </td>
                    `;

                    categoryList.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-category').forEach(button => {
                    button.addEventListener('click', () => editCategory(button.dataset.id, button.dataset.name));
                });

                document.querySelectorAll('.delete-category').forEach(button => {
                    button.addEventListener('click', () => showDeleteCategoryModal(button.dataset.id));
                });
            } catch (error) {
                console.error('Error loading categories with count:', error);
                categoryList.innerHTML = '<tr><td colspan="3" class="px-6 py-4 text-center text-red-500">Fehler beim Laden der Kategorien.</td></tr>';
            }
        }

        // Show add category modal
        function showAddCategoryModal() {
            categoryModalTitle.textContent = 'Neue Kategorie hinzufügen';
            categoryForm.reset();
            categoryId.value = '';
            categoryModal.classList.remove('hidden');
        }

        // Show edit category modal
        function editCategory(id, name) {
            categoryModalTitle.textContent = 'Kategorie bearbeiten';
            categoryId.value = id;
            categoryName.value = name;
            categoryModal.classList.remove('hidden');
        }

        // Hide category modal
        function hideCategoryModal() {
            categoryModal.classList.add('hidden');
        }

        // Show delete category modal
        function showDeleteCategoryModal(id) {
            currentCategoryId = id;
            deleteCategoryModal.classList.remove('hidden');
        }

        // Hide delete category modal
        function hideDeleteCategoryModal() {
            deleteCategoryModal.classList.add('hidden');
        }

        // Save category
        async function saveCategory(e) {
            e.preventDefault();

            try {
                if (!categoryName.value.trim()) {
                    alert('Bitte gib einen Kategorienamen ein.');
                    return;
                }

                const categoryData = {
                    name: categoryName.value.trim()
                };

                let error;

                if (categoryId.value) {
                    // Update existing category
                    const { error: updateError } = await supabaseClient
                        .from('categories')
                        .update(categoryData)
                        .eq('id', categoryId.value);

                    error = updateError;
                } else {
                    // Add new category
                    const { error: insertError } = await supabaseClient
                        .from('categories')
                        .insert([categoryData]);

                    error = insertError;
                }

                if (error) {
                    console.error('Error saving category:', error);
                    alert('Fehler beim Speichern der Kategorie: ' + error.message);
                    return;
                }

                hideCategoryModal();
                loadCategoriesWithCount();
                loadCategories(); // Reload categories for product dropdown
            } catch (error) {
                console.error('Error saving category:', error);
                alert('Fehler beim Speichern der Kategorie: ' + error.message);
            }
        }

        // Delete category
        async function deleteCategory() {
            try {
                const { error } = await supabaseClient
                    .from('categories')
                    .delete()
                    .eq('id', currentCategoryId);

                if (error) {
                    console.error('Error deleting category:', error);
                    alert('Fehler beim Löschen der Kategorie: ' + error.message);
                    return;
                }

                hideDeleteCategoryModal();
                loadCategoriesWithCount();
                loadCategories(); // Reload categories for product dropdown
            } catch (error) {
                console.error('Error deleting category:', error);
                alert('Fehler beim Löschen der Kategorie: ' + error.message);
            }
        }

        // Debounce function for search input
        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), delay);
            };
        }
    </script>
</body>
</html>
