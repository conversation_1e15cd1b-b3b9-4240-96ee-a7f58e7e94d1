<!DOCTYPE html>
<html lang="de" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Print Masters - Individuelle 3D-Druck Lösungen</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#3B82F6',secondary:'#10B981'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <!-- Supabase JS Library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Theme JS -->
    <script src="/js/theme.js" defer></script>
    <!-- Website Content JS -->
    <script>
        // Add cache busting for website-content.js
        const scriptElement = document.createElement('script');
        scriptElement.src = `/js/website-content.js?v=${new Date().getTime()}`;
        scriptElement.defer = true;
        document.head.appendChild(scriptElement);
    </script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Poppins', sans-serif;
        }
        .hero-section {
            background-color: #f9fafb; /* Light gray background as fallback */
            background-size: cover;
            background-position: center;
        }
        /* Light mode hero overlay */
        .hero-overlay {
            background: linear-gradient(90deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(255,255,255,0.3) 100%);
            transition: background 0.3s ease;
        }
        /* Dark mode hero overlay */
        .dark .hero-overlay {
            background: linear-gradient(90deg, rgba(17,24,39,0.95) 0%, rgba(17,24,39,0.85) 50%, rgba(17,24,39,0.5) 100%);
        }
        input:focus, button:focus {
            outline: none;
        }
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        .product-card:hover .product-actions {
            opacity: 1;
        }
        .custom-checkbox {
            display: none;
        }
        .custom-checkbox + label {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            display: inline-block;
        }
        .custom-checkbox + label:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            background: #fff;
            border-radius: 4px;
        }
        .custom-checkbox:checked + label:before {
            background: #3B82F6;
            border-color: #3B82F6;
        }
        .custom-checkbox:checked + label:after {
            content: '';
            position: absolute;
            left: 7px;
            top: 3px;
            width: 6px;
            height: 11px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        .category-filter::-webkit-scrollbar {
            height: 4px;
        }
        .category-filter::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .category-filter::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 10px;
        }
        .category-filter::-webkit-scrollbar-thumb:hover {
            background: #ccc;
        }


    </style>
</head>
<body class="bg-gray-50">

    <!-- Header -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div class="container mx-auto px-4 py-3">
            <div class="grid grid-cols-3 items-center">
                <!-- Logo (Left) -->
                <div class="flex justify-start">
                    <a href="#" class="flex items-center">
                        <span class="text-3xl font-['Pacifico'] text-primary" id="logo-text" style="visibility: hidden;"></span>
                    </a>
                </div>

                <!-- Navigation (Center) -->
                <nav class="hidden md:flex items-center justify-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-primary font-medium transition">Startseite</a>
                    <a href="#new-products" class="text-gray-700 hover:text-primary font-medium transition">Neuheiten</a>
                    <a href="#products" class="text-gray-700 hover:text-primary font-medium transition">Produkte</a>
                </nav>

                <!-- Social Icons & Controls (Right) -->
                <div class="flex items-center justify-end space-x-4">
                    <a href="https://instagram.com" target="_blank" class="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary transition">
                        <i class="ri-instagram-line ri-lg"></i>
                    </a>
                    <a href="https://tiktok.com" target="_blank" class="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary transition">
                        <i class="ri-tiktok-line ri-lg"></i>
                    </a>
                    <a href="https://wa.me/1234567890" target="_blank" class="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary transition">
                        <i class="ri-whatsapp-line ri-lg"></i>
                    </a>
                    <button id="theme-toggle" class="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary transition theme-toggle" title="Zum dunklen Modus wechseln">
                        <i id="theme-icon" class="ri-moon-line ri-lg"></i>
                    </button>
                    <button class="md:hidden w-10 h-10 flex items-center justify-center" id="mobile-menu-button">
                        <i class="ri-menu-line ri-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden bg-white absolute w-full shadow-md" id="mobile-menu">
            <div class="container mx-auto px-4 py-3 flex flex-col space-y-3">
                <a href="#home" class="text-gray-700 hover:text-primary font-medium py-2 transition">Startseite</a>
                <a href="#new-products" class="text-gray-700 hover:text-primary font-medium py-2 transition">Neuheiten</a>
                <a href="#products" class="text-gray-700 hover:text-primary font-medium py-2 transition">Produkte</a>
                <div class="flex items-center py-2">
                    <button id="mobile-theme-toggle" class="flex items-center text-gray-700 hover:text-primary font-medium transition" onclick="toggleTheme()">
                        <i class="ri-moon-line mr-2"></i> <span id="mobile-theme-text">Zum dunklen Modus wechseln</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-section pt-24 relative">
        <div id="hero-background" class="absolute top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat" style="display: none;"></div>
        <div class="hero-overlay w-full h-full absolute top-0 left-0 bg-white bg-opacity-80"></div>
        <div class="container mx-auto px-4 py-16 md:py-24 relative">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div class="text-left">
                    <h1 id="hero-title" class="text-4xl md:text-5xl font-bold text-gray-900 mb-4"></h1>
                    <p id="hero-text" class="text-lg text-gray-700 mb-8"></p>
                    <div class="flex flex-wrap gap-4">
                        <a href="#products" class="bg-primary text-white px-6 py-3 !rounded-button font-medium hover:bg-blue-600 transition whitespace-nowrap">Produkte entdecken</a>
                        <a href="https://wa.me/1234567890" target="_blank" class="bg-green-500 text-white px-6 py-3 !rounded-button font-medium hover:bg-green-600 transition flex items-center whitespace-nowrap">
                            <i class="ri-whatsapp-line mr-2"></i> WhatsApp
                        </a>
                        <a href="https://instagram.com" target="_blank" class="bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 text-white px-6 py-3 !rounded-button font-medium hover:opacity-90 transition flex items-center whitespace-nowrap">
                            <i class="ri-instagram-line mr-2"></i> Instagram
                        </a>
                        <a href="https://tiktok.com" target="_blank" class="bg-black text-white px-6 py-3 !rounded-button font-medium hover:bg-gray-800 transition flex items-center whitespace-nowrap">
                            <i class="ri-tiktok-line mr-2"></i> TikTok
                        </a>
                    </div>
                </div>
                <div class="hidden md:block">
                    <!-- This space is intentionally left empty as the background image covers this area -->
                </div>
            </div>
        </div>
    </section>


    <!-- New Products Section -->
    <section id="new-products" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 id="new-products-title" class="text-3xl font-bold mb-4"></h2>
                <p id="new-products-text" class="text-gray-600 max-w-2xl mx-auto"></p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Products will be loaded dynamically -->
            </div>

            <div class="text-center mt-10">
                <a href="#products" class="bg-white border border-primary text-primary px-6 py-3 !rounded-button font-medium hover:bg-primary hover:text-white transition whitespace-nowrap">Alle Produkte ansehen</a>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 id="products-title" class="text-3xl font-bold mb-4"></h2>
                <p id="products-text" class="text-gray-600 max-w-2xl mx-auto"></p>
            </div>

            <!-- Filter Controls -->
            <div class="mb-8">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
                    <div class="flex items-center">
                        <span class="text-gray-700 font-medium mr-3">Filtern nach:</span>
                        <div class="relative">
                            <button id="category-dropdown" class="bg-white border border-gray-300 rounded-button px-4 py-2 flex items-center whitespace-nowrap">
                                <span>Alle Kategorien</span>
                                <i class="ri-arrow-down-s-line ml-2"></i>
                            </button>
                            <div id="category-menu" class="hidden absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded shadow-lg z-10 w-48">
                                <div class="p-2">
                                    <div class="mb-2">
                                        <input type="checkbox" id="cat-all" class="custom-checkbox" checked>
                                        <label for="cat-all" class="text-sm">Alle Kategorien</label>
                                    </div>
                                    <div class="mb-2">
                                        <input type="checkbox" id="cat-home" class="custom-checkbox">
                                        <label for="cat-home" class="text-sm">Wohndeko</label>
                                    </div>
                                    <div class="mb-2">
                                        <input type="checkbox" id="cat-tech" class="custom-checkbox">
                                        <label for="cat-tech" class="text-sm">Tech-Zubehör</label>
                                    </div>
                                    <div class="mb-2">
                                        <input type="checkbox" id="cat-office" class="custom-checkbox">
                                        <label for="cat-office" class="text-sm">Bürobedarf</label>
                                    </div>
                                    <div>
                                        <input type="checkbox" id="cat-toys" class="custom-checkbox">
                                        <label for="cat-toys" class="text-sm">Spielzeug & Spiele</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="relative w-full md:w-64">
                        <input type="text" placeholder="Produkte suchen..." class="w-full bg-gray-50 border border-gray-300 rounded-button px-4 py-2 pl-10 text-sm">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400">
                            <i class="ri-search-line"></i>
                        </div>
                    </div>
                </div>

                <!-- Category Pills -->
                <div class="category-filter flex items-center space-x-2 overflow-x-auto pb-2">
                    <button class="bg-primary text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">Alle</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Wohndeko</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Tech-Zubehör</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Bürobedarf</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Spielzeug & Spiele</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Küche</button>
                    <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap">Gadgets</button>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Products will be loaded dynamically -->
            </div>

            <!-- Pagination - will only be shown when there are enough products -->
            <div id="pagination-container" class="flex justify-center mt-12 hidden">
                <nav class="inline-flex rounded-md shadow-sm">
                    <a href="#" class="px-3 py-2 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 rounded-l-md whitespace-nowrap">Zurück</a>
                    <a href="#" class="px-3 py-2 bg-primary text-white border border-primary whitespace-nowrap">1</a>
                    <a href="#" class="px-3 py-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 whitespace-nowrap">2</a>
                    <a href="#" class="px-3 py-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 whitespace-nowrap">3</a>
                    <a href="#" class="px-3 py-2 bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 rounded-r-md whitespace-nowrap">Weiter</a>
                </nav>
            </div>
        </div>
    </section>

    <!-- Supabase Configuration -->
    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://gprnzxmgvotjmwhajcig.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdwcm56eG1ndm90am13aGFqY2lnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDI0NTUsImV4cCI6MjA2MDk3ODQ1NX0.GRwD7bTR998FtNy4t7NUH1LNeIsGhfq6HTtFR-0U8QQ';

        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Function to fetch all products
        async function fetchProducts(categoryId = null) {
            let query = supabaseClient
                .from('products')
                .select(`
                    *,
                    categories(name)
                `)
                .order('created_at', { ascending: false });

            if (categoryId) {
                query = query.eq('category_id', categoryId);
            }

            const { data, error } = await query;

            if (error) {
                console.error('Error fetching products:', error);
                return [];
            }

            return data;
        }

        // Function to fetch new products
        async function fetchNewProducts() {
            const { data, error } = await supabaseClient
                .from('products')
                .select(`
                    *,
                    categories(name)
                `)
                .eq('is_new', true)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching new products:', error);
                return [];
            }

            return data;
        }

        // Function to fetch categories
        async function fetchCategories() {
            const { data, error } = await supabaseClient
                .from('categories')
                .select('*')
                .order('name', { ascending: true });

            if (error) {
                console.error('Error fetching categories:', error);
                return [];
            }

            return data;
        }

        // Function to search products
        async function searchProducts(query) {
            const { data, error } = await supabaseClient
                .from('products')
                .select(`
                    *,
                    categories(name)
                `)
                .ilike('name', `%${query}%`)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error searching products:', error);
                return [];
            }

            return data;
        }

        // Load products when the page loads
        document.addEventListener('DOMContentLoaded', async function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });

            // Close mobile menu when clicking on a link
            const mobileLinks = mobileMenu.querySelectorAll('a');
            mobileLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenu.classList.add('hidden');
                });
            });

            // Always load the latest content from the database
            try {
                console.log('Initializing website content');

                // Check URL parameters for refresh flag
                const urlParams = new URLSearchParams(window.location.search);
                const refreshParam = urlParams.get('refresh');

                // Check if we need to show a notification (only when coming from admin)
                const refreshWebsiteContent = localStorage.getItem('refreshWebsiteContent');
                const refreshTimestamp = localStorage.getItem('refreshTimestamp');
                const showNotification = refreshWebsiteContent === 'true' || refreshParam === 'true';

                if (refreshWebsiteContent === 'true') {
                    console.log('Refresh flag detected in localStorage, timestamp:', refreshTimestamp);
                    // Clear the flag
                    localStorage.removeItem('refreshWebsiteContent');
                }

                // Force a direct database query to get the latest content
                console.log('Performing direct database query to get latest content');
                const { data, error } = await supabaseClient
                    .from('website_content')
                    .select('*')
                    .eq('id', 1)
                    .single();

                if (error) {
                    console.error('Error fetching content directly:', error);
                    // Fall back to the regular content loading mechanism
                    await loadWebsiteContent();
                } else if (data) {
                    console.log('Successfully fetched content directly:', data);
                    // Apply the content directly to the DOM
                    applyContentToDOM(data);

                    // Also store it in localStorage for future use
                    localStorage.setItem('websiteContent', JSON.stringify(data));
                    localStorage.setItem('websiteContentTimestamp', new Date().getTime().toString());
                } else {
                    console.error('No content returned from direct query');
                    // Fall back to the regular content loading mechanism
                    await loadWebsiteContent();
                }

                // Show notification if needed
                if (showNotification) {
                    setTimeout(() => {
                        alert('Inhalte wurden aktualisiert!');
                    }, 500);
                }
            } catch (error) {
                console.error('Error during initial content load:', error);

                // Last resort - try the regular content loading mechanism
                try {
                    await loadWebsiteContent();
                } catch (fallbackError) {
                    console.error('Even fallback content loading failed:', fallbackError);
                }
            }

            // Load categories
            await loadCategories();

            // Load products
            await loadNewProducts();
            await loadAllProducts();

            // Set up event listeners for product filtering
            setupProductFilters();
        });

        // Load categories
        async function loadCategories() {
            try {
                const categories = await fetchCategories();

                if (categories.length === 0) {
                    return;
                }

                // Update category dropdown
                const categoryMenu = document.getElementById('category-menu');
                categoryMenu.innerHTML = `
                    <div class="p-2">
                        <div class="mb-2">
                            <input type="checkbox" id="cat-all" class="custom-checkbox" checked>
                            <label for="cat-all" class="text-sm">Alle Kategorien</label>
                        </div>
                    </div>
                `;

                const categoryContainer = categoryMenu.querySelector('div');

                categories.forEach(category => {
                    const categoryItem = document.createElement('div');
                    categoryItem.className = 'mb-2';
                    categoryItem.innerHTML = `
                        <input type="checkbox" id="cat-${category.id}" class="custom-checkbox" data-category-id="${category.id}">
                        <label for="cat-${category.id}" class="text-sm">${category.name}</label>
                    `;
                    categoryContainer.appendChild(categoryItem);
                });

                // Update category pills
                const categoryFilter = document.querySelector('.category-filter');
                categoryFilter.innerHTML = `
                    <button data-category-id="all" class="bg-primary text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">Alle</button>
                `;

                categories.forEach(category => {
                    const button = document.createElement('button');
                    button.setAttribute('data-category-id', category.id);
                    button.className = 'bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-full text-sm hover:bg-gray-50 whitespace-nowrap';
                    button.textContent = category.name;
                    categoryFilter.appendChild(button);
                });
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Load new products
        async function loadNewProducts() {
            const newProductsContainer = document.querySelector('#new-products .grid');

            try {
                const newProducts = await fetchNewProducts();

                if (newProductsContainer) {
                    if (newProducts.length === 0) {
                        newProductsContainer.innerHTML = '<p class="col-span-3 text-center text-gray-500">Keine neuen Produkte verfügbar.</p>';
                        return;
                    }

                    newProductsContainer.innerHTML = '';

                    newProducts.forEach(product => {
                        const productCard = createProductCard(product, true);
                        newProductsContainer.appendChild(productCard);
                    });
                }
            } catch (error) {
                console.error('Error loading new products:', error);

                if (newProductsContainer) {
                    newProductsContainer.innerHTML = '<p class="col-span-3 text-center text-gray-500">Fehler beim Laden der Produkte.</p>';
                }
            }
        }

        // Load all products
        async function loadAllProducts(categoryId = null, searchQuery = null) {
            const productsContainer = document.querySelector('#products .grid');
            const paginationContainer = document.getElementById('pagination-container');

            try {
                let products;

                if (searchQuery) {
                    products = await searchProducts(searchQuery);
                } else if (categoryId && categoryId !== 'all') {
                    products = await fetchProducts(categoryId);
                } else {
                    products = await fetchProducts();
                }

                if (productsContainer) {
                    if (products.length === 0) {
                        productsContainer.innerHTML = '<p class="col-span-3 text-center text-gray-500">Keine Produkte gefunden.</p>';
                        if (paginationContainer) {
                            paginationContainer.classList.add('hidden');
                        }
                        return;
                    }

                    productsContainer.innerHTML = '';

                    products.forEach(product => {
                        const productCard = createProductCard(product, false);
                        productsContainer.appendChild(productCard);
                    });

                    // Show pagination only if there are enough products (more than 6)
                    if (paginationContainer) {
                        if (products.length > 6) {
                            paginationContainer.classList.remove('hidden');
                        } else {
                            paginationContainer.classList.add('hidden');
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading products:', error);

                if (productsContainer) {
                    productsContainer.innerHTML = '<p class="col-span-3 text-center text-gray-500">Fehler beim Laden der Produkte.</p>';
                }

                if (paginationContainer) {
                    paginationContainer.classList.add('hidden');
                }
            }
        }

        // Create product card
        function createProductCard(product, isNewProduct) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-lg shadow-sm overflow-hidden product-card group';

            const priceFormatted = new Intl.NumberFormat('de-DE', {
                style: 'currency',
                currency: 'EUR'
            }).format(product.price);

            const categoryName = product.categories ? product.categories.name : '';

            // Create the card structure
            card.innerHTML = `
                <div class="relative h-64 overflow-hidden">
                    <div class="w-full h-full bg-white"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                    ${product.is_new ? '<div class="absolute top-3 right-3 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full">NEU</div>' : ''}
                </div>
                <div class="p-6">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-xl font-semibold">${product.name}</h3>
                        <span class="text-sm text-gray-500">${categoryName}</span>
                    </div>
                    <p class="text-gray-600 mb-4">${product.description}</p>
                    <div class="flex justify-between items-center">
                        <span class="font-bold text-primary">${priceFormatted}</span>
                        <a href="https://wa.me/1234567890?text=Ich%20interessiere%20mich%20f%C3%BCr%20${encodeURIComponent(product.name)}" target="_blank" class="inline-flex items-center text-primary hover:text-blue-700 font-medium whitespace-nowrap">
                            <i class="ri-whatsapp-line mr-2"></i> Anfragen
                        </a>
                    </div>
                </div>
            `;

            // If there's an image URL, preload it and replace the placeholder
            if (product.image_url) {
                const imgContainer = card.querySelector('.relative.h-64.overflow-hidden div:first-child');
                const img = new Image();

                img.onload = function() {
                    // Replace the placeholder with the actual image
                    imgContainer.outerHTML = `<img src="${product.image_url}" alt="${product.name}" class="w-full h-full object-cover object-top transition duration-300 group-hover:scale-105">`;
                };

                img.onerror = function() {
                    // Keep the white background if image fails to load
                    console.error('Failed to load product image:', product.image_url);
                };

                // Start loading the image
                img.src = product.image_url;
            }

            return card;
        }

        // Set up product filters
        function setupProductFilters() {
            // Category dropdown toggle
            const categoryDropdown = document.getElementById('category-dropdown');
            const categoryMenu = document.getElementById('category-menu');

            categoryDropdown.addEventListener('click', function() {
                categoryMenu.classList.toggle('hidden');
            });

            // Close category dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!categoryDropdown.contains(event.target) && !categoryMenu.contains(event.target)) {
                    categoryMenu.classList.add('hidden');
                }
            });

            // Category pills
            document.querySelector('.category-filter').addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON') {
                    // Remove active class from all buttons
                    document.querySelectorAll('.category-filter button').forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('bg-white', 'border', 'border-gray-300', 'text-gray-700');
                    });

                    // Add active class to clicked button
                    e.target.classList.remove('bg-white', 'border', 'border-gray-300', 'text-gray-700');
                    e.target.classList.add('bg-primary', 'text-white');

                    // Load products for selected category
                    loadAllProducts(e.target.getAttribute('data-category-id'));
                }
            });

            // Category checkboxes
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('custom-checkbox')) {
                    if (e.target.id === 'cat-all' && e.target.checked) {
                        // Uncheck all other checkboxes
                        document.querySelectorAll('.custom-checkbox:not(#cat-all)').forEach(checkbox => {
                            checkbox.checked = false;
                        });

                        // Load all products
                        loadAllProducts();
                    } else if (e.target.checked) {
                        // Uncheck 'All Categories' checkbox
                        document.getElementById('cat-all').checked = false;

                        // Load products for selected category
                        loadAllProducts(e.target.getAttribute('data-category-id'));
                    } else {
                        // If no category is selected, select 'All Categories'
                        const anyChecked = Array.from(document.querySelectorAll('.custom-checkbox:not(#cat-all)')).some(cb => cb.checked);
                        if (!anyChecked) {
                            document.getElementById('cat-all').checked = true;
                            loadAllProducts();
                        }
                    }
                }
            });

            // Search input
            const searchInput = document.querySelector('input[placeholder="Produkte suchen..."]');
            searchInput.addEventListener('input', debounce(function(e) {
                const query = e.target.value.trim();

                if (query.length >= 2) {
                    loadAllProducts(null, query);
                } else if (query.length === 0) {
                    loadAllProducts();
                }
            }, 300));
        }

        // Debounce function for search input
        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), delay);
            };
        }
    </script>
</body>
</html>
