// Website Content Management Functions

// Function to fetch website content
async function fetchWebsiteContent() {
    try {
        console.log('Fetching website content');

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        console.log('Using timestamp for cache busting:', timestamp);

        // Directly fetch the record with ID 1
        const { data, error } = await supabaseClient
            .from('website_content')
            .select('*')
            .eq('id', 1)
            .single();

        console.log('Fetch result:', { data, error });

        if (error) {
            console.error('Error fetching website content:', error);
            throw error;
        }

        if (!data) {
            console.error('No website content found in database');
            throw new Error('No website content found');
        }

        // Log the content we're returning
        console.log('Successfully fetched content:', data);

        return data;
    } catch (error) {
        console.error('Error fetching website content:', error);
        console.error('Error details:', error.message, error.stack);

        // Instead of returning default values, we'll retry the fetch
        // This ensures users always see the actual content
        console.log('Retrying fetch after error...');

        try {
            // Wait a moment before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Use a different approach for the retry - direct SQL query
            const { data: retryData, error: retryError } = await supabaseClient
                .rpc('get_website_content');

            if (retryError || !retryData || retryData.length === 0) {
                console.error('Retry failed:', retryError);

                // Last resort - try a direct fetch again
                const { data: lastData, error: lastError } = await supabaseClient
                    .from('website_content')
                    .select('*')
                    .eq('id', 1)
                    .single();

                if (lastError || !lastData) {
                    console.error('Last resort fetch failed:', lastError);
                    throw lastError || new Error('No data returned on last resort fetch');
                }

                console.log('Last resort fetch succeeded:', lastData);
                return lastData;
            }

            console.log('Retry succeeded:', retryData);
            return retryData[0];
        } catch (retryError) {
            console.error('All fetch attempts failed:', retryError);
            // We'll let the calling function handle this error
            throw retryError;
        }
    }
}

// Function to update website content
async function updateWebsiteContent(content) {
    try {
        console.log('Updating website content with:', content);

        // We know there's already a record with ID 1 from our direct test
        // Let's try to update it directly
        console.log('Updating content with ID 1');
        const result = await supabaseClient
            .from('website_content')
            .update(content)
            .eq('id', 1);

        console.log('Update result:', result);

        if (result.error) {
            console.error('Error updating website content:', result.error);

            // If update fails, try inserting
            console.log('Update failed, trying insert instead');
            const insertResult = await supabaseClient
                .from('website_content')
                .insert([content]);

            console.log('Insert result:', insertResult);

            if (insertResult.error) {
                console.error('Error inserting website content:', insertResult.error);
                return false;
            }

            return true;
        }

        console.log('Content updated successfully');
        return true;
    } catch (error) {
        console.error('Error updating website content:', error);
        console.error('Error details:', error.message, error.stack);
        return false;
    }
}

// Function to load website content into the main website
async function loadWebsiteContent() {
    try {
        console.log('Loading website content into the main website');

        // Force a fresh fetch from the server with a timestamp to prevent caching
        const timestamp = new Date().getTime();
        console.log('Using timestamp for cache busting:', timestamp);

        // Try to get content from the database
        const content = await fetchWebsiteContent();

        if (!content) {
            throw new Error('No content returned from fetchWebsiteContent');
        }

        // Store the content in localStorage for persistence across page loads
        localStorage.setItem('websiteContent', JSON.stringify(content));
        localStorage.setItem('websiteContentTimestamp', timestamp.toString());

        console.log('Content loaded and will be applied to DOM:', content);

        // Apply the content to the DOM
        applyContentToDOM(content);

        console.log('Website content loaded and applied successfully');

        // Check if we need to show a notification (only when coming from admin)
        if (localStorage.getItem('refreshWebsiteContent') === 'true') {
            console.log('Refresh flag detected in localStorage');
            localStorage.removeItem('refreshWebsiteContent');

            // Show a notification
            setTimeout(() => {
                alert('Inhalte wurden aktualisiert!');
            }, 500);
        }

        return true;
    } catch (error) {
        console.error('Error loading website content:', error);
        console.error('Error details:', error.message, error.stack);

        // Try to load from localStorage as a fallback
        try {
            const cachedContent = localStorage.getItem('websiteContent');
            if (cachedContent) {
                console.log('Loading content from localStorage cache');
                const content = JSON.parse(cachedContent);

                // Apply the cached content to the DOM
                applyContentToDOM(content);

                console.log('Successfully loaded and applied content from cache');
                return true;
            } else {
                console.error('No cached content available');

                // Last resort - try a direct database query
                try {
                    console.log('Attempting direct database query as last resort');
                    const { data, error } = await supabaseClient
                        .from('website_content')
                        .select('*')
                        .limit(1);

                    if (error || !data || data.length === 0) {
                        console.error('Last resort query failed:', error);
                        return false;
                    }

                    console.log('Last resort query succeeded:', data[0]);
                    applyContentToDOM(data[0]);

                    return true;
                } catch (lastError) {
                    console.error('Last resort query failed with exception:', lastError);
                    return false;
                }
            }
        } catch (cacheError) {
            console.error('Error loading from cache:', cacheError);
            return false;
        }
    }
}

// Helper function to apply content to the DOM
function applyContentToDOM(content) {
    if (!content) {
        console.error('Cannot apply null or undefined content to DOM');
        return;
    }

    console.log('Applying content to DOM:', content);

    // Update logo - handle both text and image logos
    const logoText = document.getElementById('logo-text');

    if (!logoText) {
        console.error('Logo text element not found with ID: logo-text');
        return;
    }

    if (content.logo_type === 'image' && content.logo_image_url) {
        console.log('Using image logo:', content.logo_image_url);

        // Create a new image to preload the logo
        const img = new Image();

        // Set up event handlers
        img.onload = function() {
            // Image has loaded successfully, now apply it
            const logoImage = document.createElement('img');
            logoImage.src = content.logo_image_url;
            logoImage.alt = 'Logo';
            logoImage.className = 'h-10';

            // Replace text logo with image logo
            logoText.innerHTML = '';
            logoText.appendChild(logoImage);
            logoText.style.visibility = 'visible';
            console.log('Logo image loaded successfully');
        };

        img.onerror = function() {
            // Image failed to load, use text logo as fallback
            console.error('Failed to load logo image, using text logo as fallback');
            logoText.textContent = content.logo_text || '';
            logoText.style.visibility = 'visible';
        };

        // Start loading the image
        img.src = content.logo_image_url;
    } else {
        // Use text logo
        console.log('Using text logo:', content.logo_text);
        logoText.textContent = content.logo_text || '';
        logoText.style.visibility = 'visible';
    }

    // Update hero section - using direct IDs
    const heroTitle = document.getElementById('hero-title');
    const heroText = document.getElementById('hero-text');
    const heroBackground = document.getElementById('hero-background');

    console.log('Hero elements found by ID:', {
        heroTitle: !!heroTitle,
        heroText: !!heroText,
        heroBackground: !!heroBackground
    });

    if (heroTitle) {
        console.log('Updating hero title from', heroTitle.textContent, 'to', content.hero_title);
        heroTitle.textContent = content.hero_title || '';
    } else {
        console.error('Hero title element not found with ID: hero-title');
    }

    if (heroText) {
        console.log('Updating hero text from', heroText.textContent, 'to', content.hero_text);
        heroText.textContent = content.hero_text || '';
    } else {
        console.error('Hero text element not found with ID: hero-text');
    }

    // Update hero background image if available
    if (heroBackground && content.hero_background_image) {
        console.log('Updating hero background image');

        // Create a new image to preload the hero background
        const img = new Image();

        // Set up event handlers
        img.onload = function() {
            // Image has loaded successfully, now apply it and show the element
            heroBackground.style.backgroundImage = `url('${content.hero_background_image}')`;
            heroBackground.style.display = 'block';
            console.log('Hero background image loaded successfully');
        };

        img.onerror = function() {
            // Image failed to load, keep the element hidden
            console.error('Failed to load hero background image');
            heroBackground.style.display = 'none';
        };

        // Start loading the image
        img.src = content.hero_background_image;
    } else if (heroBackground) {
        // No hero background image in content, keep the element hidden
        console.log('No hero background image in content, using solid color fallback');
        heroBackground.style.display = 'none';
    } else {
        console.error('Hero background element not found with ID: hero-background');
    }

    // Update new products section - using direct IDs
    const newProductsTitle = document.getElementById('new-products-title');
    const newProductsText = document.getElementById('new-products-text');

    console.log('New products elements found by ID:', {
        newProductsTitle: !!newProductsTitle,
        newProductsText: !!newProductsText
    });

    if (newProductsTitle) {
        console.log('Updating new products title from', newProductsTitle.textContent, 'to', content.new_products_title);
        newProductsTitle.textContent = content.new_products_title || '';
    } else {
        console.error('New products title element not found with ID: new-products-title');
    }

    if (newProductsText) {
        console.log('Updating new products text from', newProductsText.textContent, 'to', content.new_products_text);
        newProductsText.textContent = content.new_products_text || '';
    } else {
        console.error('New products text element not found with ID: new-products-text');
    }

    // Update products section - using direct IDs
    const productsTitle = document.getElementById('products-title');
    const productsText = document.getElementById('products-text');

    console.log('Products elements found by ID:', {
        productsTitle: !!productsTitle,
        productsText: !!productsText
    });

    if (productsTitle) {
        console.log('Updating products title from', productsTitle.textContent, 'to', content.products_title);
        productsTitle.textContent = content.products_title || '';
    } else {
        console.error('Products title element not found with ID: products-title');
    }

    if (productsText) {
        console.log('Updating products text from', productsText.textContent, 'to', content.products_text);
        productsText.textContent = content.products_text || '';
    } else {
        console.error('Products text element not found with ID: products-text');
    }

    // Update social media links
    updateSocialMediaLinks(content);

    console.log('Content successfully applied to DOM');
}

// Helper function to update social media links
function updateSocialMediaLinks(content) {
    console.log('Updating social media links');

    // Update WhatsApp links
    const whatsappLinks = document.querySelectorAll('a[href^="https://wa.me"]');
    if (content.whatsapp_url && whatsappLinks.length > 0) {
        console.log('Updating WhatsApp links to:', content.whatsapp_url);
        whatsappLinks.forEach(link => {
            // Keep the query parameters if they exist
            const currentUrl = new URL(link.href);
            const queryParams = currentUrl.search;

            // Set the new base URL but keep the query parameters
            link.href = content.whatsapp_url + queryParams;
        });
    }

    // Update Instagram links
    const instagramLinks = document.querySelectorAll('a[href^="https://instagram.com"]');
    if (content.instagram_url && instagramLinks.length > 0) {
        console.log('Updating Instagram links to:', content.instagram_url);
        instagramLinks.forEach(link => {
            link.href = content.instagram_url;
        });
    }

    // Update TikTok links
    const tiktokLinks = document.querySelectorAll('a[href^="https://tiktok.com"]');
    if (content.tiktok_url && tiktokLinks.length > 0) {
        console.log('Updating TikTok links to:', content.tiktok_url);
        tiktokLinks.forEach(link => {
            link.href = content.tiktok_url;
        });
    }

    console.log('Social media links updated successfully');
}
